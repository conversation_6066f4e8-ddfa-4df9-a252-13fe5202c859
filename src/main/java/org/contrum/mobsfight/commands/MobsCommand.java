package org.contrum.mobsfight.commands;

import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.EntityType;
import org.contrum.mobsfight.Mobsfight;
import org.contrum.mobsfight.ai.SimpleMobAIManager;
import org.contrum.mobsfight.utils.MobTypeUtils;

import java.util.*;
import java.util.stream.Collectors;

public class MobsCommand implements CommandExecutor, TabCompleter {
    
    private final Mobsfight plugin;
    private final SimpleMobAIManager aiManager;
    
    public MobsCommand(Mobsfight plugin, SimpleMobAIManager aiManager) {
        this.plugin = plugin;
        this.aiManager = aiManager;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("mobsfight.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "attack":
                return handleAttackCommand(sender, args);
            case "remove":
                return handleRemoveCommand(sender, args);
            case "list":
                return handleListCommand(sender);
            case "clear":
                return handleClearCommand(sender);
            case "help":
                sendHelpMessage(sender);
                return true;
            default:
                sender.sendMessage(ChatColor.RED + "Unknown subcommand: " + subCommand);
                sendHelpMessage(sender);
                return true;
        }
    }
    
    private boolean handleAttackCommand(CommandSender sender, String[] args) {
        if (args.length != 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /mobs attack <mob_type1> <mob_type2>");
            sender.sendMessage(ChatColor.YELLOW + "Example: /mobs attack evokers zombies");
            return true;
        }
        
        String mob1Name = args[1];
        String mob2Name = args[2];
        
        EntityType mob1Type = MobTypeUtils.parseMobType(mob1Name);
        EntityType mob2Type = MobTypeUtils.parseMobType(mob2Name);
        
        if (mob1Type == null) {
            sender.sendMessage(ChatColor.RED + "Unknown mob type: " + mob1Name);
            return true;
        }
        
        if (mob2Type == null) {
            sender.sendMessage(ChatColor.RED + "Unknown mob type: " + mob2Name);
            return true;
        }
        
        if (mob1Type == mob2Type) {
            sender.sendMessage(ChatColor.RED + "Cannot make a mob type attack itself!");
            return true;
        }
        
        if (!MobTypeUtils.isHostileMob(mob1Type)) {
            sender.sendMessage(ChatColor.RED + MobTypeUtils.getDisplayName(mob1Type) + " cannot be made hostile!");
            return true;
        }
        
        if (!MobTypeUtils.isHostileMob(mob2Type)) {
            sender.sendMessage(ChatColor.RED + MobTypeUtils.getDisplayName(mob2Type) + " cannot be made hostile!");
            return true;
        }
        
        // Add bidirectional targeting
        boolean success1 = aiManager.addTargetingRule(mob1Type, mob2Type);
        boolean success2 = aiManager.addTargetingRule(mob2Type, mob1Type);
        
        if (success1 && success2) {
            sender.sendMessage(ChatColor.GREEN + "Successfully configured " + 
                ChatColor.YELLOW + MobTypeUtils.getDisplayName(mob1Type) + 
                ChatColor.GREEN + " and " + 
                ChatColor.YELLOW + MobTypeUtils.getDisplayName(mob2Type) + 
                ChatColor.GREEN + " to attack each other!");
            
            sender.sendMessage(ChatColor.GRAY + "This will apply to newly spawned mobs and existing mobs in loaded chunks.");
        } else {
            sender.sendMessage(ChatColor.RED + "Failed to configure mob targeting. Check console for errors.");
        }
        
        return true;
    }
    
    private boolean handleRemoveCommand(CommandSender sender, String[] args) {
        if (args.length != 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /mobs remove <mob_type1> <mob_type2>");
            return true;
        }
        
        String mob1Name = args[1];
        String mob2Name = args[2];
        
        EntityType mob1Type = MobTypeUtils.parseMobType(mob1Name);
        EntityType mob2Type = MobTypeUtils.parseMobType(mob2Name);
        
        if (mob1Type == null || mob2Type == null) {
            sender.sendMessage(ChatColor.RED + "Invalid mob types specified!");
            return true;
        }
        
        aiManager.removeTargetingRule(mob1Type, mob2Type);
        aiManager.removeTargetingRule(mob2Type, mob1Type);
        
        sender.sendMessage(ChatColor.GREEN + "Removed targeting rule between " + 
            ChatColor.YELLOW + MobTypeUtils.getDisplayName(mob1Type) + 
            ChatColor.GREEN + " and " + 
            ChatColor.YELLOW + MobTypeUtils.getDisplayName(mob2Type));
        
        return true;
    }
    
    private boolean handleListCommand(CommandSender sender) {
        Map<String, Set<EntityType>> rules = aiManager.getAllRules();
        
        if (rules.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "No mob targeting rules are currently active.");
            return true;
        }
        
        sender.sendMessage(ChatColor.GREEN + "Active mob targeting rules:");
        sender.sendMessage(ChatColor.GRAY + "─────────────────────────────");
        
        for (Map.Entry<String, Set<EntityType>> entry : rules.entrySet()) {
            EntityType attacker = EntityType.valueOf(entry.getKey());
            Set<EntityType> targets = entry.getValue();
            
            if (!targets.isEmpty()) {
                String targetNames = targets.stream()
                    .map(MobTypeUtils::getDisplayName)
                    .collect(Collectors.joining(ChatColor.GRAY + ", " + ChatColor.WHITE));
                
                sender.sendMessage(ChatColor.YELLOW + MobTypeUtils.getDisplayName(attacker) + 
                    ChatColor.GRAY + " → " + ChatColor.WHITE + targetNames);
            }
        }
        
        return true;
    }
    
    private boolean handleClearCommand(CommandSender sender) {
        aiManager.clearAllRules();
        sender.sendMessage(ChatColor.GREEN + "All mob targeting rules have been cleared!");
        return true;
    }
    
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== MobsFight Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/mobs attack <mob1> <mob2>" + ChatColor.GRAY + " - Make two mob types fight each other");
        sender.sendMessage(ChatColor.YELLOW + "/mobs remove <mob1> <mob2>" + ChatColor.GRAY + " - Remove targeting rule between two mobs");
        sender.sendMessage(ChatColor.YELLOW + "/mobs list" + ChatColor.GRAY + " - List all active targeting rules");
        sender.sendMessage(ChatColor.YELLOW + "/mobs clear" + ChatColor.GRAY + " - Clear all targeting rules");
        sender.sendMessage(ChatColor.YELLOW + "/mobs help" + ChatColor.GRAY + " - Show this help message");
        sender.sendMessage("");
        sender.sendMessage(ChatColor.GREEN + "Example: " + ChatColor.WHITE + "/mobs attack evokers zombies");
        sender.sendMessage(ChatColor.GRAY + "This will make evokers and zombies attack each other!");
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("mobsfight.admin")) {
            return new ArrayList<>();
        }
        
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // First argument - subcommands
            List<String> subCommands = Arrays.asList("attack", "remove", "list", "clear", "help");
            return subCommands.stream()
                .filter(cmd -> cmd.toLowerCase().startsWith(args[0].toLowerCase()))
                .collect(Collectors.toList());
        }
        
        if (args.length == 2 || args.length == 3) {
            // Second and third arguments - mob types
            if (args[0].equalsIgnoreCase("attack") || args[0].equalsIgnoreCase("remove")) {
                List<String> mobTypes = Arrays.asList(
                    "zombie", "zombies", "skeleton", "skeletons", "creeper", "creepers",
                    "spider", "spiders", "enderman", "endermen", "witch", "witches",
                    "evoker", "evokers", "vindicator", "vindicators", "pillager", "pillagers",
                    "ravager", "ravagers", "piglin", "piglins", "zombified_piglin", "zombified_piglins",
                    "hoglin", "hoglins", "zoglin", "zoglins", "blaze", "blazes",
                    "ghast", "ghasts", "wither_skeleton", "wither_skeletons",
                    "stray", "strays", "husk", "husks", "drowned", "phantom", "phantoms",
                    "vex", "vexes", "guardian", "guardians", "elder_guardian", "elder_guardians",
                    "shulker", "shulkers", "silverfish", "endermite", "endermites",
                    "cave_spider", "cave_spiders", "slime", "slimes", "magma_cube", "magma_cubes",
                    "warden", "wardens"
                );
                
                String currentArg = args[args.length - 1].toLowerCase();
                return mobTypes.stream()
                    .filter(type -> type.startsWith(currentArg))
                    .collect(Collectors.toList());
            }
        }
        
        return completions;
    }
}
