package org.contrum.mobsfight.utils;

import org.bukkit.entity.EntityType;
import java.util.HashMap;
import java.util.Map;

public class MobTypeUtils {
    
    private static final Map<String, EntityType> MOB_TYPE_MAP = new HashMap<>();
    
    static {
        // Initialize common mob types with their string representations
        MOB_TYPE_MAP.put("zombie", EntityType.ZOMBIE);
        MOB_TYPE_MAP.put("zombies", EntityType.ZOMBIE);
        MOB_TYPE_MAP.put("skeleton", EntityType.SKELETON);
        MOB_TYPE_MAP.put("skeletons", EntityType.SKELETON);
        MOB_TYPE_MAP.put("creeper", EntityType.CREEPER);
        MOB_TYPE_MAP.put("creepers", EntityType.CREEPER);
        MOB_TYPE_MAP.put("spider", EntityType.SPIDER);
        MOB_TYPE_MAP.put("spiders", EntityType.SPIDER);
        MOB_TYPE_MAP.put("enderman", EntityType.ENDERMAN);
        MOB_TYPE_MAP.put("endermen", EntityType.ENDERMAN);
        MOB_TYPE_MAP.put("witch", EntityType.WITCH);
        MOB_TYPE_MAP.put("witches", EntityType.WITCH);
        MOB_TYPE_MAP.put("evoker", EntityType.EVOKER);
        MOB_TYPE_MAP.put("evokers", EntityType.EVOKER);
        MOB_TYPE_MAP.put("vindicator", EntityType.VINDICATOR);
        MOB_TYPE_MAP.put("vindicators", EntityType.VINDICATOR);
        MOB_TYPE_MAP.put("pillager", EntityType.PILLAGER);
        MOB_TYPE_MAP.put("pillagers", EntityType.PILLAGER);
        MOB_TYPE_MAP.put("ravager", EntityType.RAVAGER);
        MOB_TYPE_MAP.put("ravagers", EntityType.RAVAGER);
        MOB_TYPE_MAP.put("piglin", EntityType.PIGLIN);
        MOB_TYPE_MAP.put("piglins", EntityType.PIGLIN);
        MOB_TYPE_MAP.put("zombified_piglin", EntityType.ZOMBIFIED_PIGLIN);
        MOB_TYPE_MAP.put("zombified_piglins", EntityType.ZOMBIFIED_PIGLIN);
        MOB_TYPE_MAP.put("hoglin", EntityType.HOGLIN);
        MOB_TYPE_MAP.put("hoglins", EntityType.HOGLIN);
        MOB_TYPE_MAP.put("zoglin", EntityType.ZOGLIN);
        MOB_TYPE_MAP.put("zoglins", EntityType.ZOGLIN);
        MOB_TYPE_MAP.put("blaze", EntityType.BLAZE);
        MOB_TYPE_MAP.put("blazes", EntityType.BLAZE);
        MOB_TYPE_MAP.put("ghast", EntityType.GHAST);
        MOB_TYPE_MAP.put("ghasts", EntityType.GHAST);
        MOB_TYPE_MAP.put("wither_skeleton", EntityType.WITHER_SKELETON);
        MOB_TYPE_MAP.put("wither_skeletons", EntityType.WITHER_SKELETON);
        MOB_TYPE_MAP.put("stray", EntityType.STRAY);
        MOB_TYPE_MAP.put("strays", EntityType.STRAY);
        MOB_TYPE_MAP.put("husk", EntityType.HUSK);
        MOB_TYPE_MAP.put("husks", EntityType.HUSK);
        MOB_TYPE_MAP.put("drowned", EntityType.DROWNED);
        MOB_TYPE_MAP.put("phantom", EntityType.PHANTOM);
        MOB_TYPE_MAP.put("phantoms", EntityType.PHANTOM);
        MOB_TYPE_MAP.put("vex", EntityType.VEX);
        MOB_TYPE_MAP.put("vexes", EntityType.VEX);
        MOB_TYPE_MAP.put("guardian", EntityType.GUARDIAN);
        MOB_TYPE_MAP.put("guardians", EntityType.GUARDIAN);
        MOB_TYPE_MAP.put("elder_guardian", EntityType.ELDER_GUARDIAN);
        MOB_TYPE_MAP.put("elder_guardians", EntityType.ELDER_GUARDIAN);
        MOB_TYPE_MAP.put("shulker", EntityType.SHULKER);
        MOB_TYPE_MAP.put("shulkers", EntityType.SHULKER);
        MOB_TYPE_MAP.put("silverfish", EntityType.SILVERFISH);
        MOB_TYPE_MAP.put("endermite", EntityType.ENDERMITE);
        MOB_TYPE_MAP.put("endermites", EntityType.ENDERMITE);
        MOB_TYPE_MAP.put("cave_spider", EntityType.CAVE_SPIDER);
        MOB_TYPE_MAP.put("cave_spiders", EntityType.CAVE_SPIDER);
        MOB_TYPE_MAP.put("slime", EntityType.SLIME);
        MOB_TYPE_MAP.put("slimes", EntityType.SLIME);
        MOB_TYPE_MAP.put("magma_cube", EntityType.MAGMA_CUBE);
        MOB_TYPE_MAP.put("magma_cubes", EntityType.MAGMA_CUBE);
        MOB_TYPE_MAP.put("warden", EntityType.WARDEN);
        MOB_TYPE_MAP.put("wardens", EntityType.WARDEN);
    }
    
    /**
     * Parse a string mob type name to EntityType
     * @param mobTypeName The string name of the mob type
     * @return EntityType if found, null otherwise
     */
    public static EntityType parseMobType(String mobTypeName) {
        if (mobTypeName == null) {
            return null;
        }
        
        String normalized = mobTypeName.toLowerCase().trim();
        return MOB_TYPE_MAP.get(normalized);
    }
    
    /**
     * Check if a mob type is hostile/can be made to fight
     * @param entityType The entity type to check
     * @return true if the mob can be made hostile
     */
    public static boolean isHostileMob(EntityType entityType) {
        switch (entityType) {
            case ZOMBIE:
            case SKELETON:
            case CREEPER:
            case SPIDER:
            case CAVE_SPIDER:
            case ENDERMAN:
            case WITCH:
            case EVOKER:
            case VINDICATOR:
            case PILLAGER:
            case RAVAGER:
            case PIGLIN:
            case ZOMBIFIED_PIGLIN:
            case HOGLIN:
            case ZOGLIN:
            case BLAZE:
            case GHAST:
            case WITHER_SKELETON:
            case STRAY:
            case HUSK:
            case DROWNED:
            case PHANTOM:
            case VEX:
            case GUARDIAN:
            case ELDER_GUARDIAN:
            case SHULKER:
            case SILVERFISH:
            case ENDERMITE:
            case SLIME:
            case MAGMA_CUBE:
            case WARDEN:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * Get a user-friendly name for an EntityType
     * @param entityType The entity type
     * @return A formatted name
     */
    public static String getDisplayName(EntityType entityType) {
        if (entityType == null) {
            return "Unknown";
        }
        
        String name = entityType.name().toLowerCase().replace("_", " ");
        return name.substring(0, 1).toUpperCase() + name.substring(1);
    }
}
