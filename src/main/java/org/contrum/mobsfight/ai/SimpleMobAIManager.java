package org.contrum.mobsfight.ai;

import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.entity.*;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.mobsfight.Mobsfight;
import org.contrum.mobsfight.utils.MobTypeUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class SimpleMobAIManager {
    
    private final Mobsfight plugin;
    private final Map<String, Set<EntityType>> targetingRules = new ConcurrentHashMap<>();
    private final Set<UUID> managedMobs = ConcurrentHashMap.newKeySet();
    private BukkitRunnable aiTask;
    
    public SimpleMobAIManager(Mobsfight plugin) {
        this.plugin = plugin;
        startAITask();
    }
    
    private void startAITask() {
        aiTask = new BukkitRunnable() {
            @Override
            public void run() {
                processAllMobs();
            }
        };
        aiTask.runTaskTimer(plugin, 20L, 20L); // Run every second
    }
    
    public boolean addTargetingRule(EntityType attacker, EntityType target) {
        if (!MobTypeUtils.isHostileMob(attacker) || !MobTypeUtils.isHostileMob(target)) {
            return false;
        }
        
        String key = attacker.name();
        targetingRules.computeIfAbsent(key, k -> new HashSet<>()).add(target);
        
        plugin.getLogger().info("Added targeting rule: " + attacker.name() + " -> " + target.name());
        return true;
    }
    
    public void removeTargetingRule(EntityType attacker, EntityType target) {
        String key = attacker.name();
        Set<EntityType> targets = targetingRules.get(key);
        if (targets != null) {
            targets.remove(target);
            if (targets.isEmpty()) {
                targetingRules.remove(key);
            }
        }
    }
    
    public Set<EntityType> getTargets(EntityType attacker) {
        return targetingRules.getOrDefault(attacker.name(), new HashSet<>());
    }
    
    public void applyRulesToMob(LivingEntity entity) {
        if (!(entity instanceof Mob)) {
            return;
        }
        
        managedMobs.add(entity.getUniqueId());
    }
    
    private void processAllMobs() {
        if (targetingRules.isEmpty()) {
            return;
        }
        
        for (World world : Bukkit.getWorlds()) {
            for (Entity entity : world.getEntities()) {
                if (!(entity instanceof Mob)) {
                    continue;
                }
                
                Mob mob = (Mob) entity;
                EntityType mobType = mob.getType();
                
                // Check if this mob type has targeting rules
                Set<EntityType> targets = getTargets(mobType);
                if (targets.isEmpty()) {
                    continue;
                }
                
                // Find nearby entities to target
                LivingEntity currentTarget = mob.getTarget();
                if (currentTarget != null && !currentTarget.isDead() && 
                    targets.contains(currentTarget.getType())) {
                    // Already targeting a valid target
                    continue;
                }
                
                // Look for new targets
                LivingEntity newTarget = findNearestTarget(mob, targets);
                if (newTarget != null) {
                    mob.setTarget(newTarget);
                }
            }
        }
        
        // Clean up dead mobs from managed set
        managedMobs.removeIf(uuid -> {
            Entity entity = Bukkit.getEntity(uuid);
            return entity == null || entity.isDead();
        });
    }
    
    private LivingEntity findNearestTarget(Mob mob, Set<EntityType> targetTypes) {
        double maxRange = 16.0; // Maximum targeting range
        LivingEntity nearestTarget = null;
        double nearestDistance = maxRange;
        
        for (Entity entity : mob.getNearbyEntities(maxRange, maxRange, maxRange)) {
            if (!(entity instanceof LivingEntity)) {
                continue;
            }
            
            LivingEntity livingEntity = (LivingEntity) entity;
            
            // Check if this entity type should be targeted
            if (!targetTypes.contains(livingEntity.getType())) {
                continue;
            }
            
            // Skip dead entities
            if (livingEntity.isDead()) {
                continue;
            }
            
            // Skip entities that are already being targeted by this mob
            if (mob.getTarget() == livingEntity) {
                continue;
            }
            
            // Check line of sight (basic check)
            if (!mob.hasLineOfSight(livingEntity)) {
                continue;
            }
            
            double distance = mob.getLocation().distance(livingEntity.getLocation());
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestTarget = livingEntity;
            }
        }
        
        return nearestTarget;
    }
    
    public Map<String, Set<EntityType>> getAllRules() {
        return new HashMap<>(targetingRules);
    }
    
    public void clearAllRules() {
        targetingRules.clear();
        managedMobs.clear();
    }
    
    public void shutdown() {
        if (aiTask != null) {
            aiTask.cancel();
        }
        clearAllRules();
    }
}
