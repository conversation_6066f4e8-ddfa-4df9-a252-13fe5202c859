package org.contrum.mobsfight.ai;

import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Mob;
import org.contrum.mobsfight.Mobsfight;
import org.contrum.mobsfight.utils.MobTypeUtils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class MobAIManager {
    
    private final Mobsfight plugin;
    private final Map<String, Set<EntityType>> targetingRules = new ConcurrentHashMap<>();
    
    // NMS reflection fields and methods
    private Class<?> craftEntityClass;
    private Class<?> entityClass;
    private Class<?> mobClass;
    private Class<?> goalSelectorClass;
    private Class<?> pathfinderGoalClass;
    private Class<?> pathfinderGoalNearestAttackableTargetClass;
    private Method getHandleMethod;
    private Field goalSelectorField;
    private Field targetSelectorField;
    private Method addGoalMethod;
    private Method removeGoalMethod;
    
    public MobAIManager(Mobsfight plugin) {
        this.plugin = plugin;
        initializeReflection();
    }
    
    private void initializeReflection() {
        try {
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];

            // Try modern mapping first (1.17+)
            try {
                craftEntityClass = Class.forName("org.bukkit.craftbukkit." + version + ".entity.CraftEntity");
                getHandleMethod = craftEntityClass.getMethod("getHandle");

                // Modern NMS classes (mapped)
                entityClass = Class.forName("net.minecraft.world.entity.Entity");
                mobClass = Class.forName("net.minecraft.world.entity.Mob");
                goalSelectorClass = Class.forName("net.minecraft.world.entity.ai.goal.GoalSelector");
                pathfinderGoalClass = Class.forName("net.minecraft.world.entity.ai.goal.Goal");
                pathfinderGoalNearestAttackableTargetClass = Class.forName("net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal");

                // Modern field names
                goalSelectorField = mobClass.getDeclaredField("goalSelector");
                goalSelectorField.setAccessible(true);
                targetSelectorField = mobClass.getDeclaredField("targetSelector");
                targetSelectorField.setAccessible(true);

                // Modern method names
                addGoalMethod = goalSelectorClass.getDeclaredMethod("addGoal", int.class, pathfinderGoalClass);
                addGoalMethod.setAccessible(true);

                plugin.getLogger().info("Using modern NMS mappings");

            } catch (Exception modernEx) {
                // Fall back to legacy mappings
                plugin.getLogger().info("Modern mappings failed, trying legacy mappings...");

                String nmsPackage = "net.minecraft.server." + version;
                String craftBukkitPackage = "org.bukkit.craftbukkit." + version;

                craftEntityClass = Class.forName(craftBukkitPackage + ".entity.CraftEntity");
                getHandleMethod = craftEntityClass.getMethod("getHandle");

                entityClass = Class.forName(nmsPackage + ".Entity");
                mobClass = Class.forName(nmsPackage + ".EntityInsentient");
                goalSelectorClass = Class.forName(nmsPackage + ".PathfinderGoalSelector");
                pathfinderGoalClass = Class.forName(nmsPackage + ".PathfinderGoal");
                pathfinderGoalNearestAttackableTargetClass = Class.forName(nmsPackage + ".PathfinderGoalNearestAttackableTarget");

                goalSelectorField = mobClass.getDeclaredField("goalSelector");
                goalSelectorField.setAccessible(true);
                targetSelectorField = mobClass.getDeclaredField("targetSelector");
                targetSelectorField.setAccessible(true);

                addGoalMethod = goalSelectorClass.getDeclaredMethod("a", int.class, pathfinderGoalClass);
                addGoalMethod.setAccessible(true);

                plugin.getLogger().info("Using legacy NMS mappings");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("Failed to initialize NMS reflection: " + e.getMessage());
            plugin.getLogger().severe("This plugin may not work properly on this server version!");
            e.printStackTrace();
        }
    }
    
    public boolean addTargetingRule(EntityType attacker, EntityType target) {
        if (!MobTypeUtils.isHostileMob(attacker) || !MobTypeUtils.isHostileMob(target)) {
            return false;
        }
        
        String key = attacker.name();
        targetingRules.computeIfAbsent(key, k -> new HashSet<>()).add(target);
        
        // Apply to existing mobs
        applyRuleToExistingMobs(attacker, target);
        
        return true;
    }
    
    public void removeTargetingRule(EntityType attacker, EntityType target) {
        String key = attacker.name();
        Set<EntityType> targets = targetingRules.get(key);
        if (targets != null) {
            targets.remove(target);
            if (targets.isEmpty()) {
                targetingRules.remove(key);
            }
        }
    }
    
    public Set<EntityType> getTargets(EntityType attacker) {
        return targetingRules.getOrDefault(attacker.name(), new HashSet<>());
    }
    
    public void applyRulesToMob(LivingEntity entity) {
        if (!(entity instanceof Mob)) {
            return;
        }
        
        EntityType entityType = entity.getType();
        Set<EntityType> targets = getTargets(entityType);
        
        if (targets.isEmpty()) {
            return;
        }
        
        try {
            Object nmsEntity = getHandleMethod.invoke(entity);
            if (!mobClass.isInstance(nmsEntity)) {
                return;
            }
            
            Object targetSelector = targetSelectorField.get(nmsEntity);
            
            // Add targeting goals for each target type
            for (EntityType targetType : targets) {
                addTargetingGoal(targetSelector, nmsEntity, targetType);
            }
            
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to apply AI rules to mob: " + e.getMessage());
        }
    }
    
    private void addTargetingGoal(Object targetSelector, Object nmsEntity, EntityType targetType) {
        try {
            // Create a custom targeting goal
            Object targetingGoal = createCustomTargetingGoal(nmsEntity, targetType);
            if (targetingGoal != null) {
                addGoalMethod.invoke(targetSelector, 2, targetingGoal);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to add targeting goal: " + e.getMessage());
        }
    }
    
    private Object createCustomTargetingGoal(Object nmsEntity, EntityType targetType) {
        try {
            // Create a custom targeting goal that targets specific entity types
            return new CustomTargetGoal(nmsEntity, targetType, this);
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create custom targeting goal: " + e.getMessage());
            return null;
        }
    }

    // Inner class for custom targeting goal
    private class CustomTargetGoal {
        private final Object mobEntity;
        private final EntityType targetType;
        private final MobAIManager manager;

        public CustomTargetGoal(Object mobEntity, EntityType targetType, MobAIManager manager) {
            this.mobEntity = mobEntity;
            this.targetType = targetType;
            this.manager = manager;
        }

        // This would need to be implemented as a proper PathfinderGoal in a full NMS implementation
        // For now, we'll use a simpler approach with Bukkit API scheduling
    }
    
    private void applyRuleToExistingMobs(EntityType attacker, EntityType target) {
        Bukkit.getScheduler().runTask(plugin, () -> {
            for (World world : Bukkit.getWorlds()) {
                for (Entity entity : world.getEntities()) {
                    if (entity.getType() == attacker && entity instanceof LivingEntity) {
                        applyRulesToMob((LivingEntity) entity);
                    }
                }
            }
        });
    }
    
    public Map<String, Set<EntityType>> getAllRules() {
        return new HashMap<>(targetingRules);
    }
    
    public void clearAllRules() {
        targetingRules.clear();
    }
}
