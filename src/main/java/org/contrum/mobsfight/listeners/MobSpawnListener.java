package org.contrum.mobsfight.listeners;

import org.bukkit.entity.LivingEntity;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.CreatureSpawnEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.mobsfight.Mobsfight;
import org.contrum.mobsfight.ai.SimpleMobAIManager;

public class MobSpawnListener implements Listener {
    
    private final Mobsfight plugin;
    private final SimpleMobAIManager aiManager;
    
    public MobSpawnListener(Mobsfight plugin, SimpleMobAIManager aiManager) {
        this.plugin = plugin;
        this.aiManager = aiManager;
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onMobSpawn(CreatureSpawnEvent event) {
        LivingEntity entity = event.getEntity();
        
        // Apply AI modifications after a short delay to ensure the entity is fully initialized
        new BukkitRunnable() {
            @Override
            public void run() {
                if (entity.isValid() && !entity.isDead()) {
                    aiManager.applyRulesToMob(entity);
                }
            }
        }.runTaskLater(plugin, 1L); // 1 tick delay
    }
}
