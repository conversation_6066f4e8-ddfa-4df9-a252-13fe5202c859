package org.contrum.mobsfight;

import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;
import org.contrum.mobsfight.ai.SimpleMobAIManager;
import org.contrum.mobsfight.commands.MobsCommand;
import org.contrum.mobsfight.listeners.MobSpawnListener;

public final class Mobsfight extends JavaPlugin {

    private SimpleMobAIManager aiManager;
    private MobsCommand mobsCommand;
    private MobSpawnListener mobSpawnListener;

    @Override
    public void onEnable() {
        getLogger().info("MobsFight plugin is starting...");

        // Initialize AI Manager
        aiManager = new SimpleMobAIManager(this);

        // Initialize and register command
        mobsCommand = new MobsCommand(this, aiManager);
        getCommand("mobs").setExecutor(mobsCommand);
        getCommand("mobs").setTabCompleter(mobsCommand);

        // Initialize and register event listener
        mobSpawnListener = new MobSpawnListener(this, aiManager);
        Bukkit.getPluginManager().registerEvents(mobSpawnListener, this);

        getLogger().info("MobsFight plugin has been enabled successfully!");
        getLogger().info("Use /mobs help to see available commands.");
    }

    @Override
    public void onDisable() {
        getLogger().info("MobsFight plugin is shutting down...");

        // Shutdown AI manager
        if (aiManager != null) {
            aiManager.shutdown();
        }

        getLogger().info("MobsFight plugin has been disabled.");
    }

    public SimpleMobAIManager getAIManager() {
        return aiManager;
    }
}
