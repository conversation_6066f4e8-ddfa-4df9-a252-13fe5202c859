# 🔧 Instrucciones de Instalación - MobsFight Plugin

## 📋 Requisitos Previos

1. **Java 17 o superior** instalado
2. **Maven 3.6 o superior** instalado
3. **Servidor Spigot/Paper 1.20.1**

## 🚀 Compilación Rápida

### Opción 1: Script Automático (Recomendado)
1. Ejecuta `compile.bat` 
2. El script verificará Maven y compilará automáticamente
3. El JAR se generará en `target\mobsfight-1.0-SNAPSHOT.jar`

### Opción 2: Manual con Maven
```bash
mvn clean package
```

## 📦 Instalación en el Servidor

1. **Copia** el archivo `mobsfight-1.0-SNAPSHOT.jar` a la carpeta `plugins` de tu servidor
2. **Reinicia** el servidor
3. **Verifica** que el plugin se cargó correctamente en los logs

## ⚙️ Configuración de Maven (Si no tienes Maven)

### Windows:
1. **Descarga** Ma<PERSON> desde: https://maven.apache.org/download.cgi
2. **Extrae** el archivo a `C:\apache-maven-3.9.5` (o similar)
3. **Agrega** `C:\apache-maven-3.9.5\bin` a tu variable PATH:
   - Presiona `Win + R`, escribe `sysdm.cpl`
   - Ve a "Opciones avanzadas" → "Variables de entorno"
   - En "Variables del sistema", busca "Path" y edítala
   - Agrega la ruta de Maven
4. **Reinicia** tu terminal/cmd
5. **Verifica** con: `mvn -version`

### Linux/Mac:
```bash
# Ubuntu/Debian
sudo apt install maven

# CentOS/RHEL
sudo yum install maven

# macOS (con Homebrew)
brew install maven
```

## 🎮 Uso del Plugin

Una vez instalado, usa estos comandos:

```
/mobs attack evokers zombies    # Hace que evokers y zombies se ataquen
/mobs list                      # Lista reglas activas
/mobs remove evokers zombies    # Remueve una regla
/mobs clear                     # Limpia todas las reglas
/mobs help                      # Muestra ayuda
```

## 🔑 Permisos

Agrega este permiso a los usuarios que puedan usar el plugin:
```yaml
permissions:
  mobsfight.admin: true
```

## 🐛 Solución de Problemas

### Error: "mvn no se reconoce como comando"
- **Solución**: Instala Maven y agrégalo al PATH (ver instrucciones arriba)

### Error: "Plugin failed to load"
- **Verifica** que estés usando Java 17+
- **Verifica** que el servidor sea Spigot/Paper 1.20.1
- **Revisa** los logs del servidor para más detalles

### Error: "Command not found"
- **Verifica** que el plugin se cargó correctamente
- **Verifica** que tienes el permiso `mobsfight.admin`

### Los mobs no se atacan
- **Usa** `/mobs list` para verificar que las reglas estén activas
- **Verifica** que los mobs estén cerca (máximo 16 bloques)
- **Asegúrate** de usar tipos de mobs válidos (ver README.md)

## 📞 Soporte

Si tienes problemas:
1. **Revisa** este archivo de instalación
2. **Consulta** el README.md para uso del plugin
3. **Verifica** los logs del servidor para errores específicos

## ✅ Verificación de Instalación Exitosa

Cuando el plugin se instale correctamente, verás estos mensajes en los logs:

```
[INFO] MobsFight plugin is starting...
[INFO] MobsFight plugin has been enabled successfully!
[INFO] Use /mobs help to see available commands.
```

¡Listo! Tu plugin MobsFight está funcionando correctamente.
