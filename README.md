# MobsFight Plugin

Un plugin de Minecraft que permite modificar la IA de los mobs para que se ataquen entre sí usando comandos simples.

## Características

- Comando simple `/mobs attack <mob_type1> <mob_type2>` para hacer que dos tipos de mobs se ataquen mutuamente
- Soporte para una amplia variedad de mobs hostiles
- Autocompletado de comandos con Tab
- Sistema de targeting bidireccional (ambos tipos de mobs se atacarán entre sí)
- Aplicación automática a mobs existentes y nuevos
- Gestión de reglas con comandos para listar, remover y limpiar

## Comandos

### Comando Principal
- `/mobs attack <mob_type1> <mob_type2>` - Hace que dos tipos de mobs se ataquen entre sí
- `/mobs remove <mob_type1> <mob_type2>` - Remueve una regla de targeting entre dos mobs
- `/mobs list` - Lista todas las reglas de targeting activas
- `/mobs clear` - <PERSON>pia todas las reglas de targeting
- `/mobs help` - Muestra la ayuda de comandos

### Ejemplos de Uso
```
/mobs attack evokers zombies
/mobs attack skeletons creepers
/mobs attack piglins hoglins
/mobs remove evokers zombies
/mobs list
/mobs clear
```

## Tipos de Mobs Soportados

El plugin soporta los siguientes tipos de mobs (puedes usar singular o plural):

- `zombie` / `zombies`
- `skeleton` / `skeletons`
- `creeper` / `creepers`
- `spider` / `spiders`
- `cave_spider` / `cave_spiders`
- `enderman` / `endermen`
- `witch` / `witches`
- `evoker` / `evokers`
- `vindicator` / `vindicators`
- `pillager` / `pillagers`
- `ravager` / `ravagers`
- `piglin` / `piglins`
- `zombified_piglin` / `zombified_piglins`
- `hoglin` / `hoglins`
- `zoglin` / `zoglins`
- `blaze` / `blazes`
- `ghast` / `ghasts`
- `wither_skeleton` / `wither_skeletons`
- `stray` / `strays`
- `husk` / `husks`
- `drowned`
- `phantom` / `phantoms`
- `vex` / `vexes`
- `guardian` / `guardians`
- `elder_guardian` / `elder_guardians`
- `shulker` / `shulkers`
- `silverfish`
- `endermite` / `endermites`
- `slime` / `slimes`
- `magma_cube` / `magma_cubes`
- `warden` / `wardens`

## Permisos

- `mobsfight.admin` - Permiso para usar todos los comandos del plugin

## Instalación

1. Compila el plugin usando Maven: `mvn clean package`
2. Copia el archivo JAR generado a la carpeta `plugins` de tu servidor
3. Reinicia el servidor
4. El plugin estará listo para usar

## Compilación

Requisitos:
- Java 17 o superior
- Maven 3.6 o superior

Comandos:
```bash
mvn clean compile
mvn clean package
```

El archivo JAR se generará en la carpeta `target/`.

## Compatibilidad

- Minecraft 1.20.1
- Spigot/Paper
- Java 17+

## Funcionamiento

El plugin funciona de la siguiente manera:

1. Cuando ejecutas `/mobs attack <mob1> <mob2>`, se crea una regla bidireccional
2. El plugin ejecuta una tarea cada segundo que revisa todos los mobs en el servidor
3. Para cada mob que tiene reglas de targeting, busca objetivos válidos en un radio de 16 bloques
4. Si encuentra un objetivo válido del tipo correcto, lo establece como target del mob
5. Los mobs nuevos que aparezcan también serán afectados por las reglas activas

## Notas Técnicas

- El plugin usa la API de Bukkit en lugar de NMS para mayor compatibilidad
- Las reglas se almacenan en memoria y se pierden al reiniciar el servidor
- El sistema de targeting tiene un alcance máximo de 16 bloques
- Se verifica la línea de visión antes de establecer un objetivo
- El plugin limpia automáticamente referencias a mobs muertos

## Soporte

Si encuentras algún problema o tienes sugerencias, por favor reporta el issue con detalles sobre:
- Versión del servidor
- Versión de Java
- Mensaje de error (si aplica)
- Pasos para reproducir el problema
