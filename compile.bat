@echo off
echo ========================================
echo MobsFight Plugin Compilation Script
echo ========================================
echo.

REM Check if Maven is available
where mvn >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Maven found! Compiling with Maven...
    mvn clean package
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo SUCCESS! Plugin compiled successfully!
        echo ========================================
        echo JAR file location: target\mobsfight-1.0-SNAPSHOT.jar
        echo.
        echo Copy this JAR to your server's plugins folder.
        pause
        exit /b 0
    ) else (
        echo.
        echo ========================================
        echo COMPILATION FAILED!
        echo ========================================
        echo Please check the error messages above.
        pause
        exit /b 1
    )
) else (
    echo Maven not found in PATH!
    echo.
    echo Please install Maven or add it to your PATH:
    echo 1. Download Maven from: https://maven.apache.org/download.cgi
    echo 2. Extract it to a folder (e.g., C:\apache-maven-3.9.5)
    echo 3. Add C:\apache-maven-3.9.5\bin to your PATH environment variable
    echo 4. Restart your command prompt and try again
    echo.
    pause
    exit /b 1
)
